#!/bin/sh

# Exit on any error
set -e

echo "🚀 Starting Key Manager application..."

# Ensure data directory has correct permissions
chown -R nextjs:nodejs /app/data

# Check if database exists, if not create it
if [ ! -f "/app/data/prod.db" ]; then
    echo "📦 Database not found, creating database..."
    su-exec nextjs touch /app/data/prod.db
    echo "✅ Database file created"
fi

echo "🎯 Starting Next.js application..."
# Switch to nextjs user and start the application
exec su-exec nextjs "$@"
