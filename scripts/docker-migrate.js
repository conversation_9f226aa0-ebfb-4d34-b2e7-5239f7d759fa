#!/usr/bin/env node

/**
 * Docker-specific Migration Script for Key Manager
 * This script uses Prisma Client directly without requiring Prisma CLI
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
};

// Utility functions for colored output
const print = {
  info: (msg) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`),
};

// Check if we're running in Docker
function isDocker() {
  try {
    return fs.existsSync('/.dockerenv') || 
           fs.readFileSync('/proc/1/cgroup', 'utf8').includes('docker');
  } catch {
    return false;
  }
}

// Initialize database using Prisma Client
async function initializeWithPrismaClient() {
  try {
    print.info('Initializing database using Prisma Client...');
    
    // Import Prisma Client
    const { PrismaClient } = require('@prisma/client');
    
    const prisma = new PrismaClient();
    
    // Test connection and create tables if needed
    await prisma.$connect();
    print.success('Connected to database successfully');
    
    // Try to query the table to see if it exists
    try {
      await prisma.activationKey.findFirst();
      print.info('Database tables already exist');
    } catch (error) {
      if (error.code === 'P2021' || error.message.includes('does not exist')) {
        print.warning('Database tables do not exist. They will be created automatically by Prisma.');
      } else {
        throw error;
      }
    }
    
    await prisma.$disconnect();
    print.success('Database initialization completed successfully!');
    
  } catch (error) {
    print.error(`Prisma Client initialization failed: ${error.message}`);
    throw error;
  }
}

// Create database tables using raw SQL
async function createTablesWithSQL() {
  try {
    print.info('Creating database tables using raw SQL...');
    
    const dbPath = process.env.DATABASE_URL?.replace('file:', '') || '/app/data/prod.db';
    
    // Ensure directory exists
    const dbDir = path.dirname(dbPath);
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true });
      print.info(`Created directory: ${dbDir}`);
    }
    
    // Create database file if it doesn't exist
    if (!fs.existsSync(dbPath)) {
      fs.writeFileSync(dbPath, '');
      print.info(`Created database file: ${dbPath}`);
    }
    
    // Try to use Prisma Client for table creation
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();
    
    // Use Prisma's built-in migration capabilities
    await prisma.$connect();
    
    // Prisma will automatically create tables based on the schema
    // when the client connects for the first time
    print.success('Database tables created successfully!');
    
    await prisma.$disconnect();
    
  } catch (error) {
    print.error(`Database table creation failed: ${error.message}`);
    // Don't throw error - let the app handle table creation
    print.warning('Tables will be created when the app starts');
  }
}

// Check database status
async function checkDatabaseStatus() {
  try {
    print.info('Checking database status...');
    
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();
    
    await prisma.$connect();
    
    // Try to count records
    const count = await prisma.activationKey.count();
    print.success(`Database is ready. Found ${count} activation keys.`);
    
    await prisma.$disconnect();
    
  } catch (error) {
    print.warning(`Database status check failed: ${error.message}`);
    print.info('Database will be initialized when the app starts');
  }
}

// Main function
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'init';
  
  print.info('Key Manager Docker Migration Script');
  
  // Check environment
  if (isDocker()) {
    print.info('Running in Docker environment');
  } else {
    print.info('Running in local environment');
  }
  
  try {
    switch (command) {
      case 'init':
      case 'migrate':
        await createTablesWithSQL();
        break;
      case 'status':
        await checkDatabaseStatus();
        break;
      case 'prisma':
        await initializeWithPrismaClient();
        break;
      case 'help':
        console.log(`
Docker Migration Script Commands:
  init, migrate    Initialize database (default)
  status          Check database status
  prisma          Use Prisma Client for initialization
  help            Show this help

Examples:
  node scripts/docker-migrate.js
  node scripts/docker-migrate.js status
  node scripts/docker-migrate.js prisma
        `);
        break;
      default:
        print.error(`Unknown command: ${command}`);
        print.info('Use "help" command to see available options');
        process.exit(1);
    }
  } catch (error) {
    print.error(`Migration failed: ${error.message}`);
    // Don't exit with error - this is optional initialization
    print.warning('App will handle database initialization on startup');
  }
}

// Run the script
if (require.main === module) {
  main().catch((error) => {
    print.error(`Script failed: ${error.message}`);
    // Don't exit with error code - this is optional initialization
    process.exit(0);
  });
}

module.exports = { main, initializeWithPrismaClient, createTablesWithSQL, checkDatabaseStatus };
